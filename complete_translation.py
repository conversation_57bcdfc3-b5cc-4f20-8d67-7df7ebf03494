#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整翻译脚本 - 不依赖JSON文件，基于游戏术语规律完成所有翻译
"""

import re
import sys

def is_english_line(text):
    """判断是否为英文行"""
    if not text:
        return False
    
    # 如果包含中文字符，则不是英文行
    if re.search(r'[\u4e00-\u9fff]', text):
        return False
    
    # 如果只包含数字、符号和空格，则不是英文行
    if re.match(r'^[\d\s\|\[\]（）\(\)＋\+\-\.\,\，\。\！\?\？\:：\;；]*$', text):
        return False
    
    # 如果包含英文字母，则认为是英文行
    if re.search(r'[a-zA-Z]', text):
        return True
    
    return False

def create_comprehensive_translation_dict():
    """创建全面的翻译字典"""
    translation_dict = {
        # 之前已翻译的条目
        "Crimson Meteorite Grease": "深红陨石油脂",
        "Meteorite Grease": "陨石油脂", 
        "Ghostflame Grease": "鬼火油脂",
        "Messmerfire Grease": "梅瑟莫火油脂",
        "Dragonbolt Grease": "龙雷油脂",
        "Royal Magic Grease": "王室魔力油脂",
        "Golden Grease": "黄金油脂",
        "Bloodflame Grease": "血焰油脂",
        "Soul of Cinder": "薪王之魂",
        "Dante": "但丁",
        "Berserk": "狂战士",
        "Dragon Slayer": "屠龙者",
        "Slave Knight": "奴隶骑士",
        "Destined One": "天命之子",
        "Viper": "毒蛇",
        "Undead Legion": "不死军团",
        "Marvelous Chester": "奇妙的切斯特",
        "Pirate": "海盗",
        "Gunslinger": "枪手",
        "YoRHa": "寄叶部队",
        "Starscourge": "碎星",
        "Highlander": "高地人",
        "Thunder": "雷霆",
        "Flesh": "血肉",
        "Maiden": "少女",
        "Crossbreed": "混血",
        "Achenisia": "阿克尼西亚",
        "Snow Witch": "雪巫",
        "Desert": "沙漠",
        "Sir": "爵士",
        "Vergil": "维吉尔",
        "Yaksha": "夜叉",
        "Hawkeye": "鹰眼",
        "Dragon Rock Heart": "龙岩之心",
        "Nightlord's Soul Shard": "夜王魂片",
        "Simplified Chinese": "简体中文",
        "Renna's Rimesong": "蕾娜的霜歌",
        "Smithscript Blade": "锻造文字刃",
        "Cruel Blood Oath": "残酷血誓",
        "Soldier's Handgun": "士兵手枪",
        "Light Handgun": "轻型手枪", 
        "Heavy Handgun": "重型手枪",
        "Double-Barrel Handgun": "双管手枪",
        "Full Moon Handgun": "满月手枪",
        "Composite Handgun": "复合手枪",
        "Crepus's Black-Key Blunderbuss": "克雷普斯的黑键火枪",
        "Black-Key Blunderbuss": "黑键火枪",
        "Curseblade Cirque": "咒刃马戏团",
        "Sparking Odachi": "闪电太刀",
        "Style Meter": "风格计量器",
        "Eclipse": "日食",
        "Lightning Finesse": "雷电技巧",
        "Discus Skating": "圆盘滑行",
        "Freezing Dance": "冰冻之舞",
        "Pistol Barrage": "手枪弹幕",
        "Dragon Slicer": "斩龙",
        "Golden Blink": "黄金闪烁",
        "Arcane Pull": "奥术牵引",
        "Blades of Stone": "石之刃",
        "Usable with handguns": "可与手枪配合使用",
        "Thank You For Playing!": "感谢游玩！",
        "Rune Denizen": "卢恩居民",
        
        # 法术和技能名称
        "Radagon's Spear": "拉达冈之矛",
        "Miriam's Vanishing": "米莉亚姆的消失",
        "Glintblade Trio": "辉剑三重奏",
        "Rellana's Twin Moons": "蕾拉娜的双月",
        "Glintstone Nail": "辉石钉",
        "Glintstone Nails": "辉石钉群",
        "Gravitational Missile": "重力导弹",
        "Mantle of Thorns": "荆棘斗篷",
        "Impenetrable Thorns": "坚不可摧的荆棘",
        "Rings of Spectral Light": "幽光之环",
        "Vortex of Putrescence": "腐败漩涡",
        "Mass of Putrescence": "腐败团块",
        "Furious Blade of Ansbach": "安斯巴赫的怒刃",
        "Heal from Afar": "远程治疗",
        "Aspects of the Crucible: Thorns": "坩埚相位：荆棘",
        "Aspects of the Crucible: Bloom": "坩埚相位：绽放",
        "Minor Erdtree": "小黄金树",
        "Land of Shadow": "阴影之地",
        "Wrath from Afar": "远程愤怒",
        "Light of Miquella": "米凯拉之光",
        "Multilayered Ring of Light": "多层光环",
        "Roar of Rugalea": "鲁加利亚的咆哮",
        "Knight's Lightning Spear": "骑士雷电长矛",
        "Dragonbolt of Florissax": "弗洛里萨克斯的龙雷",
        "Electrocharge": "电荷",
        "Bayle's Tyranny": "贝尔的暴政",
        "Bayle's Flame Lightning": "贝尔的火焰雷电",
        "Ghostflame Breath": "鬼火吐息",
        "Rotten Butterflies": "腐烂蝴蝶",
        "Pest-Thread Spears": "害虫丝线长矛",
        "Midra's Flame of Frenzy": "米德拉的癫火",
        "Fleeting Microcosm": "瞬息小宇宙",
        "Cherishing Fingers": "珍爱之指",
        "Watchful Spirit": "守望之灵",
        "Golden Arcs": "黄金弧光",
        "Giant Golden Arc": "巨大黄金弧光",
        "Spira": "螺旋",
        "Divine Beast Tornado": "神兽龙卷风",
        "Divine Bird Feathers": "神鸟羽毛",
        "Fire Serpent": "火蛇",
        "Rain of Fire": "火雨",
        "Messmer's Orb": "梅瑟莫之球",
        
        # 技能描述
        "Flame Skewer": "火焰串刺",
        "Flame Spear": "火焰长矛",
        "Divine Beast Frost Stomp": "神兽霜踏",
        "Dragonwound Slash": "伤龙斩击",
        "Needle Piercer": "针刺穿透",
        "Light": "光明",
        "Darkness": "黑暗",
        "Onze's Line of Stars": "昂泽的星线",
        "Poison Flower Blooms Twice": "毒花二度绽放",
        "Messmer's Assault": "梅瑟莫的突击",
        "White Light Charge": "白光冲锋",
        "Romina's Purification": "罗米娜的净化",
        "Jet Staff": "喷射法杖",
        "Judgement Cut": "审判斩",
        "Smithscript Arts: Spears": "锻造文字技：长矛",
        "Frostveil Dance": "霜纱之舞",
        "Triple Hammer": "三重锤击",
        
        # 状态和效果描述
        "Coats armament, inflicting great physical and magic damage": "涂抹武器，造成巨大物理和魔法伤害",
        "Coats armament, inflicting physical and magic damage": "涂抹武器，造成物理和魔法伤害",
        "Coats armament, inflicting frost and magic damage": "涂抹武器，造成冰霜和魔法伤害",
        "Coats armament, inflicting heavy fire damage": "涂抹武器，造成重火伤害",
        "Coats armament, inflicting heavy lightning damage": "涂抹武器，造成重雷伤害",
        "Coats armament, inflicting heavy magic damage": "涂抹武器，造成重魔法伤害",
        "Coats armament, inflicting heavy holy damage": "涂抹武器，造成重神圣伤害",
        "Coats armament, inflicting fire damage and blood loss": "涂抹武器，造成火焰伤害和失血",
        
        # 物品描述
        "Fugitive warrior recipe.  Adds craft able items": "逃亡战士配方。增加可制作物品",
        "Turns human frame into ancient dragon": "将人类躯体转化为古龙",
        "Turns human frame into an Aspect of the Night": "将人类躯体转化为夜之相位",
        "Successively attacking enemies increases your style rank. Gain attack boost per each rank.": "连续攻击敌人提升风格等级。每个等级获得攻击力加成。",
        
        # 制作物品描述
        "Craftable with a cracked pot.": "可用破裂的壶制作。",
        "Frost has been sealed inside.": "内部封印着冰霜。",
        "A short rope that aids throwing is attached.": "附有辅助投掷的短绳。",
        "Thrown at rearward enemies using the rope,": "使用绳索投向后方敌人，",
        "the pot explodes, causing frostbite buildup": "壶会爆炸，造成冻伤累积",
        "in enemies.": "在敌人身上。",
        
        # 肝脏干描述
        "A grey colored cured liver, dried out": "灰色的腌制肝脏，经过干燥",
        "after pickling in a medicinal solution.": "在药液中腌制后制成。",
        "Craftable item.": "可制作物品。",
        "Boosts instant death resistance": "提升即死抗性",
        "for a short time.": "持续短时间。",
        "A black-gold colored cured liver,": "黑金色的腌制肝脏，",
        "dried out after pickling in a medicinal solution.": "在药液中腌制后干燥制成。",
        "Boosts resistance to instant death": "提升对即死的抗性",
        "The blood has been properly drained,": "血液已被适当排出，",
        "giving it a longer effect than traditional": "比传统的",
        "dried meat.": "肉干效果更持久。",

        # 油脂详细描述
        "Solidified grease made from a mixture of meteorite materials.": "由陨石材料混合制成的固化油脂。",
        "Quickly coats armament, inflicting great physical and magic damage.": "快速涂抹武器，造成巨大物理和魔法伤害。",
        "Solidified grease made from a mixture of cold materials left by a Death Rite Bird.": "由死仪鸟留下的寒冷材料混合制成的固化油脂。",
        "Coats armament, inflicting frost and adding magic damage.": "涂抹武器，造成冰霜并增加魔法伤害。",
        "Solidified knotgrease made from a mixture of incendiary materials.": "由燃烧材料混合制成的固化结油脂。",
        "Coats armament, adding heavy fire damage to attacks.": "涂抹武器，为攻击增加重火伤害。",
        "Fire was a symbol of the crusade, and even Messmer's rank-and-file soldiers would wield it.": "火焰是十字军的象征，连梅瑟莫的普通士兵都会使用它。",
        "Solidified knotgrease made from a mixture of fulminating materials.": "由爆炸材料混合制成的固化结油脂。",
        "Coats armament, adding heavy lightning damage to attacks.": "涂抹武器，为攻击增加重雷伤害。",
        "Said to have been a favored tool of the dragon-cult knights who once served the prince of gold.": "据说是曾经侍奉黄金王子的龙教骑士们喜爱的工具。",
        "Solidified knotgrease made from a mixture of magically resonant materials.": "由魔法共鸣材料混合制成的固化结油脂。",
        "Coats armament, adding heavy magic damage to attacks.": "涂抹武器，为攻击增加重魔法伤害。",
        "Believed to have once been bestowed upon some Carian knights in lieu of the fellowship's characteristic swords.": "据信曾被赐予一些卡利亚骑士，以代替团契的特色剑。",
        "Solidified knotgrease made from a mixture of holy resonant materials.": "由神圣共鸣材料混合制成的固化结油脂。",
        "Coats armament, adding heavy holy damage to attacks.": "涂抹武器，为攻击增加重神圣伤害。",
        "Enchanted with an ancient Erdtree incantation.": "附有古老黄金树祷告的魔法。",
        "Such ministrations are all but a lost art in the realm of shadow.": "这样的技艺在阴影王国几乎已成失传的艺术。",
        "Solidified knotgrease made from a mixture of bloody flaming materials of origin unknown.": "由来源不明的血焰材料混合制成的固化结油脂。",

        # 卢恩和恩典描述
        "Grace that dwells within the inhabitants of": "栖息在居民体内的恩典",
        "The Lands Between; the lingering residue of gold.": "交界地；黄金的残留痕迹。",
        "Use to gain many runes.": "使用以获得大量卢恩。",
        "Grace was brought to the very fringes and lowlands": "恩典被带到了最边缘的低地",
        "of the Lands Between. And with the Golden Order": "的交界地。随着黄金律法",
        "came the weight of its history. A history of conquest": "而来的是其历史的重量。征服",
        "and subjugation.": "和征服的历史。",

        # 阿西米相关
        "An asimi that has infected a tarnished.": "感染了褪色者的阿西米。",
        "An intelligent silver sludge; asimi": "智能银色软泥；阿西米",
        "infect the body of tarnished,": "感染褪色者的身体，",
        "granting the host power.": "赋予宿主力量。",
        "A letter affixed to a graveyard crow's leg.": "系在墓地乌鸦腿上的信件。",
        "Sought after by Kalé.": "卡列所寻找的。",
        "A letter written about the caravans.": "关于商队的信件。",
        "It seems to have been sent from the": "似乎是从",
        "capital underground.": "地下王都发来的。",
        "The shedding of an Asimi that infected a tarnished.": "感染褪色者的阿西米的蜕皮。",
        "An Asimi that infected a tarnished.": "感染了褪色者的阿西米。",

        # 按键提示
        "<?keyicon@0017?>：<?keyActName@17?>": "<?keyicon@0017?>：<?keyActName@17?>",
        "<?keyicon@9?>：<?keyActName@9?>": "<?keyicon@9?>：<?keyActName@9?>",
        "<?keyicon@29?>：<?keyActName@29?>": "<?keyicon@29?>：<?keyActName@29?>",
        "<?keyicon@28?>：<?keyActName@28?>": "<?keyicon@28?>：<?keyActName@28?>",
        "<?keyicon@25?>：<?keyActName@25?>": "<?keyicon@25?>：<?keyActName@25?>",
        "<?keyicon@19?>：<?keyActName@19?>": "<?keyicon@19?>：<?keyActName@19?>",
        "<?keyicon@20?>：<?keyActName@20?>": "<?keyicon@20?>：<?keyActName@20?>",

        # 配方书描述
        "Recipe book written by a roaming warrior, chased by someone through the Lands Between.": "被人追赶穿越交界地的流浪战士所写的配方书。",
        "Adds more craftable items.": "增加更多可制作物品。",
        "Includes techniques for survival in extreme situations and prolonged battle.": "包含在极端情况和持久战斗中的生存技巧。",
        "A record of crafting techniques left by a": "一份制作技术记录，由",
        "travelling perfumer who served in the Shattering.": "在破碎战争中服役的旅行调香师留下。",
        "Expands crafting repertoire.": "扩展制作技能。",
        "-Maddening Boluses": "-发狂苔药",

        # 龙心和夜王魂片
        "Heart consumed in the ancient, original form of Dragon Communion.": "在古老原始的龙餐仪式中消耗的心脏。",
        "Use to turn one's human flesh into an ancient dragon.": "使用以将人类血肉转化为古龙。",
        "\"The last thing the partaker saw with human eyes was a sunset, its colors faded and tarnished—a remote thing from eternity.\"": "\"参与者用人类眼睛看到的最后一样东西是日落，其色彩褪色而失去光泽——来自永恒的遥远事物。\"",
        "Shard of the soul of the Lord of Night itself.": "夜之主本身灵魂的碎片。",
        "Use to turn into an Aspect of the Night, imbuing the armament and  with elemental fury and producing spontaneous elemental bursts in the proximity.": "使用以转化为夜之相位，为武器注入元素之怒并在附近产生自发的元素爆发。",

        # 手枪描述
        "A handgun of simple make, usable by anyone, but lacking in both power and accuracy.": "制作简单的手枪，任何人都能使用，但威力和精度都不足。",
        "Ranged weapon made of lighter materials. Deals good damage and is simple to use.": "由轻质材料制成的远程武器。造成良好伤害且使用简单。",
        "Heavy handgun with iron limbs, increasing damage dealt.": "带有铁制部件的重型手枪，增加造成的伤害。",
        "Handgun made with a complex mechanism and design with two cannons, so will shoot two projectiles per shot.": "采用复杂机制设计的双管手枪，每次射击发射两发弹丸。",
        "One-of-a-kind enchanted handgun of exquisitely detailed craftsmanship.": "独一无二的附魔手枪，工艺精美细致。",
        "Made to celebrate the matrimonial union, and reconciliation, between the houses of the Erdtree and the Full Moon, Leyndell and Raya Lucaria.": "为庆祝黄金树与满月、雷德尔与雷亚卢卡利亚之间的联姻和和解而制作。",
        "Pistol made from composite materials.": "由复合材料制成的手枪。",
        "Black gun featuring a long stock. Used for sniping, it has a very long range.": "带有长枪托的黑色枪械。用于狙击，射程极远。",

        # 夜之智慧
        "Wisdom of the Night for those who seek ultimate power.": "为寻求终极力量者准备的夜之智慧。",
        "Successively attacking enemies increases your style rank. The higher the rank, the higher the attack boost. Reach last rank to be able to unleash an ultimate attack.": "连续攻击敌人提升风格等级。等级越高，攻击力加成越高。达到最高等级可释放终极攻击。",
    }

    return translation_dict

def translate_file_completely(input_file, output_file):
    """完整翻译文件"""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"错误：找不到输入文件 {input_file}")
        sys.exit(1)

    # 获取翻译字典
    translation_dict = create_comprehensive_translation_dict()

    translated_lines = []
    translation_count = 0
    untranslated_items = []

    print("开始完整翻译...")

    for line_num, line in enumerate(lines, 1):
        original_line = line

        if is_english_line(line.strip()):
            english_text = line.strip()

            if english_text in translation_dict:
                chinese_text = translation_dict[english_text]
                translated_lines.append(chinese_text + '\n')
                translation_count += 1
                print(f"✓ 第{line_num}行: '{english_text}' -> '{chinese_text}'")
            else:
                # 尝试模式匹配翻译
                translated_text = pattern_based_translation(english_text)
                if translated_text != english_text:
                    translated_lines.append(translated_text + '\n')
                    translation_count += 1
                    print(f"✓ 模式第{line_num}行: '{english_text}' -> '{translated_text}'")
                else:
                    untranslated_items.append((line_num, english_text))
                    translated_lines.append(original_line)
        else:
            translated_lines.append(original_line)

    # 写入翻译后的文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(translated_lines)

        print(f"\n完整翻译完成！")
        print(f"成功翻译: {translation_count} 行")
        print(f"仍未翻译: {len(untranslated_items)} 个")
        print(f"翻译结果已保存到: {output_file}")

        if untranslated_items:
            final_untranslated_file = "final_untranslated.txt"
            with open(final_untranslated_file, 'w', encoding='utf-8') as f:
                f.write("最终仍未翻译的英文条目:\n")
                f.write("=" * 50 + "\n")
                for line_num, text in untranslated_items:
                    f.write(f"第{line_num}行: {text}\n")
            print(f"剩余未翻译条目已保存到: {final_untranslated_file}")

    except Exception as e:
        print(f"错误：无法写入输出文件 - {e}")
        sys.exit(1)

def pattern_based_translation(text):
    """基于模式的翻译"""
    # 章节翻译
    if text.startswith("Chapter"):
        return text.replace("Chapter", "章节")

    # 古董翻译
    if text == "Antique":
        return "古董"

    # 技能描述模式
    if "Skill:" in text:
        return text.replace("Skill:", "技能:")

    if "Unique Skill:" in text:
        return text.replace("Unique Skill:", "独特技能:")

    # 时间描述
    if text.endswith("sec"):
        return text.replace("sec", "秒")

    # 数量描述
    if " ×" in text:
        return text  # 保持原样，因为包含变量

    # 错误处理
    if text == "<?errorHandling?>":
        return "<?错误处理?>"

    # 游戏标题
    if "ELDEN RING NIGHTREIGN" in text:
        return text.replace("ELDEN RING NIGHTREIGN", "艾尔登法环 夜之王朝")

    # 插入名称
    if text == "<?InsertName?>":
        return "<?插入名称?>"

    # 按键相关
    if "<?key" in text and "?>" in text:
        return text  # 保持按键代码不变

    # 移动相关
    if "<?keyMove?>" in text:
        return text  # 保持按键代码不变

    # 消息相关
    if "<?bmsg?>" in text:
        return text  # 保持消息代码不变

    if "<?belongMsg?>" in text:
        return text  # 保持消息代码不变

    # 数值相关
    if "<?value?>" in text:
        return text  # 保持数值代码不变

    if "<?text1?>" in text and "<?text2?>" in text:
        return text  # 保持文本代码不变

    # 对话选项
    if text.startswith("About "):
        return text.replace("About ", "关于")

    # 战斗相关
    if text.startswith("Battle: "):
        return text.replace("Battle: ", "战斗：")

    if text.startswith("Explore: "):
        return text.replace("Explore: ", "探索：")

    # 试炼相关
    if text.startswith("Trial of "):
        return text.replace("Trial of ", "试炼：")

    if text.startswith("Ascension "):
        return text.replace("Ascension ", "飞升")

    # 远征相关
    if text == "Expedition":
        return "远征"

    if text == "Standard Expedition":
        return "标准远征"

    # 视觉效果
    if text == "Change visuals":
        return "更改视觉效果"

    if text.endswith(" aura"):
        return text.replace(" aura", "光环")

    if text.endswith(" eyes"):
        return text.replace(" eyes", "眼睛")

    if text.endswith(" phantom"):
        return text.replace(" phantom", "幻影")

    if text == "Disable visuals":
        return "禁用视觉效果"

    if text == "More visuals":
        return "更多视觉效果"

    if text == "Go back":
        return "返回"

    # 其他常见模式
    if text == "Red":
        return "红色"

    if text == "Blue":
        return "蓝色"

    if text == "Golden":
        return "黄金"

    if text == "Purple":
        return "紫色"

    if text == "Orange":
        return "橙色"

    if text == "Lightning":
        return "雷电"

    if text == "Night":
        return "夜晚"

    if text == "Ghost":
        return "幽灵"

    if text == "Steam":
        return "蒸汽"

    if text == "Glintstone":
        return "辉石"

    if text == "Crucible":
        return "坩埚"

    if text == "Distorting":
        return "扭曲"

    if text == "Evergaol":
        return "永恒监牢"

    if text == "Frenzied":
        return "癫狂"

    if text == "Vision of fire":
        return "火之幻象"

    if text == "Spirit":
        return "灵魂"

    if text == "Lord of Chaos":
        return "混沌之主"

    # 如果没有匹配的模式，返回原文
    return text

def main():
    """主函数"""
    input_file = "text1.txt"
    output_file = "text1_complete_translated.txt"

    print("完整翻译工具")
    print("=" * 60)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("=" * 60)

    # 执行完整翻译
    translate_file_completely(input_file, output_file)

if __name__ == "__main__":
    main()
