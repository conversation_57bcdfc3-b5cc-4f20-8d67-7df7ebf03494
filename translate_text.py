#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于JSON翻译字典翻译TXT文件中的英文内容
"""

import json
import re
import sys
from pathlib import Path

def load_translation_dict(json_file):
    """加载JSON翻译字典"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到翻译字典文件 {json_file}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误：JSON文件格式错误 - {e}")
        sys.exit(1)

def find_fuzzy_matches(text, translation_dict, threshold=0.8):
    """查找模糊匹配的翻译"""
    from difflib import SequenceMatcher

    best_matches = []
    for key in translation_dict:
        similarity = SequenceMatcher(None, text.lower(), key.lower()).ratio()
        if similarity >= threshold:
            best_matches.append((key, translation_dict[key], similarity))

    # 按相似度排序
    best_matches.sort(key=lambda x: x[2], reverse=True)
    return best_matches[:3]  # 返回前3个最佳匹配

def translate_text_file(input_file, output_file, translation_dict):
    """翻译文本文件"""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"错误：找不到输入文件 {input_file}")
        sys.exit(1)

    translated_lines = []
    translation_count = 0
    fuzzy_matches = []
    untranslated_items = []

    for line_num, line in enumerate(lines, 1):
        original_line = line

        # 检查是否为英文行（不包含中文字符且不是纯数字或符号）
        if is_english_line(line.strip()):
            english_text = line.strip()

            # 在翻译字典中查找对应的中文翻译
            if english_text in translation_dict:
                chinese_text = translation_dict[english_text]
                translated_lines.append(chinese_text + '\n')
                translation_count += 1
                print(f"第{line_num}行: '{english_text}' -> '{chinese_text}'")
            else:
                # 尝试模糊匹配
                matches = find_fuzzy_matches(english_text, translation_dict, 0.7)
                if matches:
                    fuzzy_matches.append((line_num, english_text, matches))
                    # 暂时保持原文，但记录可能的匹配
                    translated_lines.append(original_line)
                else:
                    # 如果找不到任何匹配，保持原文
                    translated_lines.append(original_line)
                    untranslated_items.append((line_num, english_text))
        else:
            # 非英文行保持不变
            translated_lines.append(original_line)

    # 写入翻译后的文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(translated_lines)
        print(f"\n翻译完成！")
        print(f"精确翻译: {translation_count} 行")
        print(f"模糊匹配: {len(fuzzy_matches)} 个")
        print(f"完全未匹配: {len(untranslated_items)} 个")
        print(f"翻译结果已保存到: {output_file}")

        # 保存模糊匹配的条目
        if fuzzy_matches:
            fuzzy_file = "fuzzy_matches.txt"
            with open(fuzzy_file, 'w', encoding='utf-8') as f:
                f.write("可能的模糊匹配:\n")
                f.write("=" * 50 + "\n")
                for line_num, text, matches in fuzzy_matches:
                    f.write(f"\n第{line_num}行: {text}\n")
                    f.write("可能的匹配:\n")
                    for i, (key, value, similarity) in enumerate(matches, 1):
                        f.write(f"  {i}. {key} -> {value} (相似度: {similarity:.2f})\n")
            print(f"模糊匹配已保存到: {fuzzy_file}")

        # 保存未翻译的条目到单独文件
        if untranslated_items:
            untranslated_file = "untranslated_items.txt"
            with open(untranslated_file, 'w', encoding='utf-8') as f:
                f.write("完全未找到翻译的英文条目:\n")
                f.write("=" * 50 + "\n")
                for line_num, text in untranslated_items:
                    f.write(f"第{line_num}行: {text}\n")
            print(f"未翻译条目已保存到: {untranslated_file}")

    except Exception as e:
        print(f"错误：无法写入输出文件 - {e}")
        sys.exit(1)

def is_english_line(text):
    """判断是否为英文行"""
    if not text:
        return False
    
    # 如果包含中文字符，则不是英文行
    if re.search(r'[\u4e00-\u9fff]', text):
        return False
    
    # 如果只包含数字、符号和空格，则不是英文行
    if re.match(r'^[\d\s\|\[\]（）\(\)＋\+\-\.\,\，\。\！\?\？\:：\;；]*$', text):
        return False
    
    # 如果包含英文字母，则认为是英文行
    if re.search(r'[a-zA-Z]', text):
        return True
    
    return False

def analyze_file_content(input_file):
    """分析文件内容，统计中文、英文和其他类型的行"""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"错误：找不到输入文件 {input_file}")
        return None

    chinese_lines = 0
    english_lines = 0
    other_lines = 0

    for line in lines:
        text = line.strip()
        if not text:
            other_lines += 1
        elif re.search(r'[\u4e00-\u9fff]', text):  # 包含中文字符
            chinese_lines += 1
        elif is_english_line(text):  # 英文行
            english_lines += 1
        else:  # 其他（数字、符号等）
            other_lines += 1

    return {
        'total': len(lines),
        'chinese': chinese_lines,
        'english': english_lines,
        'other': other_lines
    }

def main():
    """主函数"""
    # 文件路径
    json_file = "nightreign.json"
    input_file = "text1.txt"
    output_file = "text1_translated.txt"

    print("开始翻译任务...")
    print(f"翻译字典: {json_file}")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("-" * 50)

    # 分析文件内容
    print("分析文件内容...")
    stats = analyze_file_content(input_file)
    if stats:
        print(f"文件统计:")
        print(f"  总行数: {stats['total']}")
        print(f"  中文行: {stats['chinese']} ({stats['chinese']/stats['total']*100:.1f}%)")
        print(f"  英文行: {stats['english']} ({stats['english']/stats['total']*100:.1f}%)")
        print(f"  其他行: {stats['other']} ({stats['other']/stats['total']*100:.1f}%)")
        print("-" * 50)

    # 加载翻译字典
    translation_dict = load_translation_dict(json_file)
    print(f"已加载 {len(translation_dict)} 个翻译条目")
    print("-" * 50)

    # 执行翻译
    translate_text_file(input_file, output_file, translation_dict)

if __name__ == "__main__":
    main()
