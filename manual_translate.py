#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动翻译脚本 - 基于模式推断翻译
"""

import json
import re
import sys

def load_translation_dict(json_file):
    """加载JSON翻译字典"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到翻译字典文件 {json_file}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误：JSON文件格式错误 - {e}")
        sys.exit(1)

def is_english_line(text):
    """判断是否为英文行"""
    if not text:
        return False
    
    # 如果包含中文字符，则不是英文行
    if re.search(r'[\u4e00-\u9fff]', text):
        return False
    
    # 如果只包含数字、符号和空格，则不是英文行
    if re.match(r'^[\d\s\|\[\]（）\(\)＋\+\-\.\,\，\。\！\?\？\:：\;；]*$', text):
        return False
    
    # 如果包含英文字母，则认为是英文行
    if re.search(r'[a-zA-Z]', text):
        return True
    
    return False

def create_manual_translations():
    """创建手动翻译字典"""
    manual_dict = {
        # Grease 类型
        "Crimson Meteorite Grease": "深红陨石油脂",
        "Meteorite Grease": "陨石油脂", 
        "Ghostflame Grease": "鬼火油脂",
        "Messmerfire Grease": "梅瑟莫火油脂",
        "Dragonbolt Grease": "龙雷油脂",
        "Royal Magic Grease": "王室魔力油脂",
        "Golden Grease": "黄金油脂",
        "Bloodflame Grease": "血焰油脂",
        
        # 角色名称
        "Soul of Cinder": "薪王之魂",
        "Dante": "但丁",
        "Berserk": "狂战士",
        "Dragon Slayer": "屠龙者",
        "Slave Knight": "奴隶骑士",
        "Destined One": "天命之子",
        "Viper": "毒蛇",
        "Undead Legion": "不死军团",
        "Marvelous Chester": "奇妙的切斯特",
        "Pirate": "海盗",
        "Gunslinger": "枪手",
        "YoRHa": "寄叶部队",
        "Starscourge": "碎星",
        "Highlander": "高地人",
        "Thunder": "雷霆",
        "Flesh": "血肉",
        "Maiden": "少女",
        "Crossbreed": "混血",
        "Achenisia": "阿克尼西亚",
        "Snow Witch": "雪巫",
        "Desert": "沙漠",
        "Sir": "爵士",
        "Vergil": "维吉尔",
        "Yaksha": "夜叉",
        "Hawkeye": "鹰眼",
        
        # 物品
        "Dragon Rock Heart": "龙岩之心",
        "Nightlord's Soul Shard": "夜王魂片",
        "Simplified Chinese": "简体中文",
        
        # 武器
        "Renna's Rimesong": "蕾娜的霜歌",
        "Smithscript Blade": "锻造文字刃",
        "Cruel Blood Oath": "残酷血誓",
        
        # 手枪
        "Soldier's Handgun": "士兵手枪",
        "Light Handgun": "轻型手枪", 
        "Heavy Handgun": "重型手枪",
        "Double-Barrel Handgun": "双管手枪",
        "Full Moon Handgun": "满月手枪",
        "Composite Handgun": "复合手枪",
        "Crepus's Black-Key Blunderbuss": "克雷普斯的黑键火枪",
        "Black-Key Blunderbuss": "黑键火枪",
        "Curseblade Cirque": "咒刃马戏团",
        "Sparking Odachi": "闪电太刀",
        
        # 技能
        "Style Meter": "风格计量器",
        "Eclipse": "日食",
        "Lightning Finesse": "雷电技巧",
        "Discus Skating": "圆盘滑行",
        "Freezing Dance": "冰冻之舞",
        "Pistol Barrage": "手枪弹幕",
        "Dragon Slicer": "斩龙",
        "Golden Blink": "黄金闪烁",
        "Arcane Pull": "奥术牵引",
        
        # 其他
        "Blades of Stone": "石之刃",
        "Usable with handguns": "可与手枪配合使用",
        "Thank You For Playing!": "感谢游玩！",
        "Chapter": "章节",
        "Rune Denizen": "卢恩居民",
    }
    
    return manual_dict

def translate_with_manual_dict(input_file, output_file, json_dict, manual_dict):
    """使用手动翻译字典进行翻译"""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"错误：找不到输入文件 {input_file}")
        sys.exit(1)
    
    translated_lines = []
    json_matches = 0
    manual_matches = 0
    untranslated_items = []
    
    print("开始翻译...")
    
    for line_num, line in enumerate(lines, 1):
        original_line = line
        
        if is_english_line(line.strip()):
            english_text = line.strip()
            
            # 首先尝试JSON字典
            if english_text in json_dict:
                chinese_text = json_dict[english_text]
                translated_lines.append(chinese_text + '\n')
                json_matches += 1
                print(f"✓ JSON第{line_num}行: '{english_text}' -> '{chinese_text}'")
            # 然后尝试手动字典
            elif english_text in manual_dict:
                chinese_text = manual_dict[english_text]
                translated_lines.append(chinese_text + '\n')
                manual_matches += 1
                print(f"✓ 手动第{line_num}行: '{english_text}' -> '{chinese_text}'")
            else:
                untranslated_items.append((line_num, english_text))
                translated_lines.append(original_line)
        else:
            translated_lines.append(original_line)
    
    # 写入翻译后的文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(translated_lines)
        
        print(f"\n翻译完成！")
        print(f"JSON字典翻译: {json_matches} 行")
        print(f"手动字典翻译: {manual_matches} 行")
        print(f"总翻译数: {json_matches + manual_matches} 行")
        print(f"仍未翻译: {len(untranslated_items)} 个")
        print(f"翻译结果已保存到: {output_file}")
        
        if untranslated_items:
            remaining_file = "remaining_untranslated.txt"
            with open(remaining_file, 'w', encoding='utf-8') as f:
                f.write("仍未翻译的英文条目:\n")
                f.write("=" * 50 + "\n")
                for line_num, text in untranslated_items:
                    f.write(f"第{line_num}行: {text}\n")
            print(f"剩余未翻译条目已保存到: {remaining_file}")
            
    except Exception as e:
        print(f"错误：无法写入输出文件 - {e}")
        sys.exit(1)

def main():
    """主函数"""
    json_file = "nightreign.json"
    input_file = "text1.txt"
    output_file = "text1_final_translated.txt"
    
    print("手动翻译工具")
    print("=" * 50)
    print(f"JSON字典: {json_file}")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("=" * 50)
    
    # 加载字典
    json_dict = load_translation_dict(json_file)
    manual_dict = create_manual_translations()
    
    print(f"已加载JSON字典: {len(json_dict)} 个条目")
    print(f"已加载手动字典: {len(manual_dict)} 个条目")
    print("=" * 50)
    
    # 执行翻译
    translate_with_manual_dict(input_file, output_file, json_dict, manual_dict)

if __name__ == "__main__":
    main()
