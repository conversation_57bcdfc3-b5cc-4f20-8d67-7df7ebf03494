#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单高效的翻译脚本 - 基于JSON翻译字典翻译TXT文件中的英文内容
"""

import json
import re
import sys

def load_translation_dict(json_file):
    """加载JSON翻译字典"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到翻译字典文件 {json_file}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误：JSON文件格式错误 - {e}")
        sys.exit(1)

def is_english_line(text):
    """判断是否为英文行"""
    if not text:
        return False
    
    # 如果包含中文字符，则不是英文行
    if re.search(r'[\u4e00-\u9fff]', text):
        return False
    
    # 如果只包含数字、符号和空格，则不是英文行
    if re.match(r'^[\d\s\|\[\]（）\(\)＋\+\-\.\,\，\。\！\?\？\:：\;；]*$', text):
        return False
    
    # 如果包含英文字母，则认为是英文行
    if re.search(r'[a-zA-Z]', text):
        return True
    
    return False

def find_keyword_matches(text, translation_dict):
    """基于关键词查找可能的翻译"""
    # 提取关键词
    keywords = ['Grease', 'Pot', 'Arrow', 'Bolt', 'Dart', 'Stone', 'Meat', 'Liver']
    
    for keyword in keywords:
        if keyword in text:
            # 查找包含该关键词的所有翻译
            matches = []
            for key, value in translation_dict.items():
                if keyword in key:
                    matches.append((key, value))
            
            if matches:
                return matches[:5]  # 返回前5个匹配
    
    return []

def translate_text_file(input_file, output_file, translation_dict):
    """翻译文本文件"""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"错误：找不到输入文件 {input_file}")
        sys.exit(1)
    
    translated_lines = []
    exact_matches = 0
    keyword_matches = []
    untranslated_items = []
    
    print("开始翻译...")
    
    for line_num, line in enumerate(lines, 1):
        original_line = line
        
        # 检查是否为英文行
        if is_english_line(line.strip()):
            english_text = line.strip()
            
            # 精确匹配
            if english_text in translation_dict:
                chinese_text = translation_dict[english_text]
                translated_lines.append(chinese_text + '\n')
                exact_matches += 1
                print(f"✓ 第{line_num}行: '{english_text}' -> '{chinese_text}'")
            else:
                # 关键词匹配
                matches = find_keyword_matches(english_text, translation_dict)
                if matches:
                    keyword_matches.append((line_num, english_text, matches))
                    translated_lines.append(original_line)  # 保持原文
                else:
                    untranslated_items.append((line_num, english_text))
                    translated_lines.append(original_line)  # 保持原文
        else:
            # 非英文行保持不变
            translated_lines.append(original_line)
    
    # 写入翻译后的文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(translated_lines)
        
        print(f"\n翻译完成！")
        print(f"精确翻译: {exact_matches} 行")
        print(f"关键词匹配: {len(keyword_matches)} 个")
        print(f"完全未匹配: {len(untranslated_items)} 个")
        print(f"翻译结果已保存到: {output_file}")
        
        # 保存关键词匹配结果
        if keyword_matches:
            keyword_file = "keyword_matches.txt"
            with open(keyword_file, 'w', encoding='utf-8') as f:
                f.write("基于关键词的可能匹配:\n")
                f.write("=" * 60 + "\n")
                for line_num, text, matches in keyword_matches:
                    f.write(f"\n第{line_num}行: {text}\n")
                    f.write("可能的翻译:\n")
                    for i, (key, value) in enumerate(matches, 1):
                        f.write(f"  {i}. {key} -> {value}\n")
            print(f"关键词匹配已保存到: {keyword_file}")
        
        # 保存未翻译的条目
        if untranslated_items:
            untranslated_file = "no_matches.txt"
            with open(untranslated_file, 'w', encoding='utf-8') as f:
                f.write("完全未找到匹配的英文条目:\n")
                f.write("=" * 50 + "\n")
                for line_num, text in untranslated_items:
                    f.write(f"第{line_num}行: {text}\n")
            print(f"未匹配条目已保存到: {untranslated_file}")
            
    except Exception as e:
        print(f"错误：无法写入输出文件 - {e}")
        sys.exit(1)

def main():
    """主函数"""
    json_file = "nightreign.json"
    input_file = "text1.txt"
    output_file = "text1_translated.txt"
    
    print("简单翻译工具")
    print("=" * 50)
    print(f"翻译字典: {json_file}")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("=" * 50)
    
    # 加载翻译字典
    translation_dict = load_translation_dict(json_file)
    print(f"已加载 {len(translation_dict)} 个翻译条目")
    
    # 执行翻译
    translate_text_file(input_file, output_file, translation_dict)

if __name__ == "__main__":
    main()
